name: "Windows Release dependencies"

on:
  workflow_dispatch:
    inputs:
      xformers:
        description: 'xformers version'
        required: false
        type: string
        default: ""
      extra_dependencies:
        description: 'extra dependencies'
        required: false
        type: string
        default: ""
      cu:
        description: 'cuda version'
        required: true
        type: string
        default: "128"

      python_minor:
        description: 'python minor version'
        required: true
        type: string
        default: "12"

      python_patch:
        description: 'python patch version'
        required: true
        type: string
        default: "10"
#  push:
#    branches:
#      - master

jobs:
  build_dependencies:
    runs-on: windows-latest
    steps:
        - uses: actions/checkout@v4
        - uses: actions/setup-python@v5
          with:
            python-version: 3.${{ inputs.python_minor }}.${{ inputs.python_patch }}

        - shell: bash
          run: |
            echo "@echo off
            call update_comfyui.bat nopause
            echo -
            echo This will try to update pytorch and all python dependencies.
            echo -
            echo If you just want to update normally, close this and run update_comfyui.bat instead.
            echo -
            pause
            ..\python_embeded\python.exe -s -m pip install --upgrade torch torchvision torchaudio ${{ inputs.xformers }} --extra-index-url https://download.pytorch.org/whl/cu${{ inputs.cu }} -r ../ComfyUI/requirements.txt pygit2
            pause" > update_comfyui_and_python_dependencies.bat

            python -m pip wheel --no-cache-dir torch torchvision torchaudio ${{ inputs.xformers }} ${{ inputs.extra_dependencies }} --extra-index-url https://download.pytorch.org/whl/cu${{ inputs.cu }} -r requirements.txt pygit2 -w ./temp_wheel_dir
            python -m pip install --no-cache-dir ./temp_wheel_dir/*
            echo installed basic
            ls -lah temp_wheel_dir
            mv temp_wheel_dir cu${{ inputs.cu }}_python_deps
            tar cf cu${{ inputs.cu }}_python_deps.tar cu${{ inputs.cu }}_python_deps

        - uses: actions/cache/save@v4
          with:
            path: |
              cu${{ inputs.cu }}_python_deps.tar
              update_comfyui_and_python_dependencies.bat
            key: ${{ runner.os }}-build-cu${{ inputs.cu }}-${{ inputs.python_minor }}
